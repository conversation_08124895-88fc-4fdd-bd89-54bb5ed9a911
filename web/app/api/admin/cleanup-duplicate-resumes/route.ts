export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";
import { cleanupDuplicateEmptyResumes, findUsersWithDuplicateEmptyResumes } from "@/utils/resumeCleanup";

async function handlePostRequest(req: NextRequest) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = await req.json();
    const { action, userId } = body;

    if (action === 'cleanup-user' && userId) {
      // Clean up duplicates for a specific user
      const result = await cleanupDuplicateEmptyResumes(userId);
      return NextResponse.json(result);
    }

    if (action === 'cleanup-current-user') {
      // Clean up duplicates for the current user
      const result = await cleanupDuplicateEmptyResumes(user.id);
      return NextResponse.json(result);
    }

    if (action === 'find-users-with-duplicates') {
      // Find all users with duplicate empty resumes
      const usersWithDuplicates = await findUsersWithDuplicateEmptyResumes();
      return NextResponse.json({ 
        success: true, 
        usersWithDuplicates,
        count: usersWithDuplicates.length 
      });
    }

    if (action === 'bulk-cleanup') {
      // Clean up duplicates for all users (admin operation)
      // Note: This should be used carefully and might need additional admin checks
      const usersWithDuplicates = await findUsersWithDuplicateEmptyResumes();
      const results = [];
      
      for (const userId of usersWithDuplicates) {
        const result = await cleanupDuplicateEmptyResumes(userId);
        results.push({ userId, ...result });
      }

      const totalRemoved = results.reduce((sum, result) => sum + result.removedCount, 0);
      const successCount = results.filter(result => result.success).length;

      return NextResponse.json({
        success: true,
        processedUsers: results.length,
        successfulCleanups: successCount,
        totalRemovedResumes: totalRemoved,
        results
      });
    }

    return NextResponse.json({ error: "Invalid action" }, { status: 400 });

  } catch (error) {
    console.error('Cleanup duplicate resumes error:', error);
    return NextResponse.json({
      error: 'Failed to cleanup duplicate resumes'
    }, { status: 500 });
  }
}

async function handleGetRequest(req: NextRequest) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    // Get statistics about duplicate resumes for the current user
    const usersWithDuplicates = await findUsersWithDuplicateEmptyResumes();
    const currentUserHasDuplicates = usersWithDuplicates.includes(user.id);

    return NextResponse.json({
      success: true,
      currentUserHasDuplicates,
      totalUsersWithDuplicates: usersWithDuplicates.length
    });

  } catch (error) {
    console.error('Get duplicate resumes stats error:', error);
    return NextResponse.json({
      error: 'Failed to get duplicate resumes statistics'
    }, { status: 500 });
  }
}

export const POST = createApiHandler(
  "cleanup-duplicate-resumes-post",
  handlePostRequest
);

export const GET = createApiHandler(
  "cleanup-duplicate-resumes-get",
  handleGetRequest
);
