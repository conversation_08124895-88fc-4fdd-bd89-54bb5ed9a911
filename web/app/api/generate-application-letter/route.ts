export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@/lib/supabase-server';
import { createApiHandler } from '@/utils/apiErrorHandler';
import { captureApiError } from '@/utils/errorMonitoring';
import { trackApiUsage } from '@/lib/mixpanel-server';
import { getTemplateById } from '@/utils/letter-templates/applicationLetterTemplates';
import { ResumeInput, JobInput } from '@/types/resume';
import { callGenerateLetterEdgeFunction, LetterEdgeFunctionRequest } from '@/utils/letterEdgeFunction';
import { convertStructuredDataToPlainText } from '@/types/letter-structured';
import { fillLetterTemplate } from '@/utils/letter-template-engine';

/**
 * Delete PDF from Supabase storage
 */
async function deletePdfFromStorage(supabase: any, letterId: string): Promise<boolean> {
  try {
    const filePath = `${letterId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-letters')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting PDF from storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting PDF from storage:', error);
    return false;
  }
}

async function handleRequest(request: NextRequest, _context?: { params: Record<string, string | string[]> }): Promise<NextResponse> {
  const startTime = Date.now();
  let userId: string | undefined;

  try {
    const formData = await request.formData();
    
    const jobDescription = formData.get('jobDescription') as string | null;
    const jobImage = formData.get('jobImage') as File | null;
    const unauthenticatedResumeFile = formData.get('unauthenticatedResumeFile') as File | null;
    const unauthenticatedResumeFileName = formData.get('unauthenticatedResumeFileName') as string | null;
    const templateId = formData.get('templateId') as string | 'plain-text';
    const existingLetterId = formData.get('existingLetterId') as string | null;
    const editedStructuredDataStr = formData.get('editedStructuredData') as string | null;
    
    // Fix validation logic
    if (!jobDescription && !jobImage) {
      return NextResponse.json({
        error: 'Deskripsi pekerjaan atau gambar lowongan diperlukan'
      }, { status: 400 });
    }
    
    // Get access token from request header
    const accessToken = request.headers.get('x-supabase-auth');

    if (!accessToken) {
      return NextResponse.json({ error: 'Missing authentication token' }, { status: 401 });
    }

    // Create Supabase client
    const supabase = await createClient();
        
    // Get user with the provided token
    const { data: { user }, error: userError } = await supabase.auth.getUser(accessToken);

    if (userError || !user) {
      console.error('Error getting user with token:', userError);
      return NextResponse.json({
        error: 'Anda harus login untuk menggunakan fitur ini'
      }, { status: 401 });
    }
    userId = user.id;

    const selectedTemplate = getTemplateById(templateId)
    if (!selectedTemplate) {
      return NextResponse.json({ error: 'Template tidak ditemukan' }, { status: 404 });
    }

    // Prepare resume input
    const resumeInput: ResumeInput = {};
    let resumeFileName: string | null = null;

    if (unauthenticatedResumeFile && unauthenticatedResumeFileName) {
      const buffer = await unauthenticatedResumeFile.arrayBuffer();
      resumeFileName = unauthenticatedResumeFileName.toLowerCase();
      
      // Get the correct mime type based on file extension
      let mimeType: string;
      if (resumeFileName && resumeFileName.endsWith('.pdf')) {
        mimeType = 'application/pdf';
      } else if (resumeFileName && resumeFileName.endsWith('.docx')) {
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      } else if (resumeFileName && resumeFileName.endsWith('.png')) {
        mimeType = 'image/png';
      } else if (resumeFileName && (resumeFileName.endsWith('.jpg') || resumeFileName.endsWith('.jpeg'))) {
        mimeType = 'image/jpeg';
      } else {
        mimeType = 'text/plain';
      }
      
      resumeInput.file = { buffer, mimeType };
    } else {
      // Get resume data from Supabase storage
      const { data: profile, error: fetchError } = await supabase
        .from('profiles')
        .select('resume_file_name, tokens')
        .eq('id', userId)
        .single();

      if (fetchError || !profile) {
        return NextResponse.json({
          error: 'Resume tidak ditemukan. Harap unggah resume terlebih dahulu.'
        }, { status: 404 });
      }

      // Check token balance against template cost
      const requiredTokens = selectedTemplate?.tokenCost ?? 0;
      const currentTokens = (profile.tokens as number | null) ?? 0;

      if (!existingLetterId && !editedStructuredDataStr && requiredTokens > 0 && currentTokens < requiredTokens) {
        return NextResponse.json({
          error: 'Token Anda tidak cukup untuk menggunakan template ini. Silakan top up token Anda.'
        }, { status: 402 });
      }

      // Get the resume file from storage
      const { data: resumeFile, error: storageError } = await supabase.storage
        .from('resumes')
        .download(profile.resume_file_name);

      if (storageError || !resumeFile) {
        captureApiError('generate-application-letter', storageError || 'Resume file access error', {
          userId,
          type: 'resume_storage_access',
          resumeFileName: profile.resume_file_name
        });
        return NextResponse.json({
          error: 'Tidak dapat mengakses file resume. Harap unggah ulang resume Anda.'
        }, { status: 500 });
      }

      // Convert the file to buffer and determine correct mime type
      const buffer = await resumeFile.arrayBuffer();
      resumeFileName = profile.resume_file_name.toLowerCase();
      
      let mimeType: string;
      if (resumeFileName && resumeFileName.endsWith('.pdf')) {
        mimeType = 'application/pdf';
      } else if (resumeFileName && resumeFileName.endsWith('.docx')) {
        mimeType = 'application/vnd.openxmlformats-officedocument.wordprocessingml.document';
      } else if (resumeFileName && resumeFileName.endsWith('.png')) {
        mimeType = 'image/png';
      } else if (resumeFileName && (resumeFileName.endsWith('.jpg') || resumeFileName.endsWith('.jpeg'))) {
        mimeType = 'image/jpeg';
      } else {
        mimeType = 'text/plain';
      }
      
      resumeInput.file = { buffer, mimeType };
    }

    // Prepare job input
    const jobInput: JobInput = {};
    if (jobDescription) {
      jobInput.description = jobDescription;
    }
    if (jobImage) {
      const buffer = await jobImage.arrayBuffer();
      jobInput.image = {
        buffer,
        mimeType: jobImage.type
      };
    }

    if (existingLetterId && editedStructuredDataStr) {
      const editedStructuredData = JSON.parse(editedStructuredDataStr)
      const plainText = convertStructuredDataToPlainText(editedStructuredData)

      const designHtml = fillLetterTemplate(selectedTemplate, editedStructuredData)
      const { error: saveError } = await supabase
        .from('letters')
        .update({
          structured_data: editedStructuredData,
          design_html: designHtml,
          plain_text: plainText,
          updated_at: new Date().toISOString()
        })
        .eq('id', existingLetterId)
        .eq('user_id', userId)
        .select('id')
        .single();

      if (saveError) {
        console.error('Error updating letter:', saveError);
      } else {
        // Delete cached PDF from storage since the letter content has been updated
        if (existingLetterId) {
          const deletionResult = await deletePdfFromStorage(supabase, existingLetterId);
          if (deletionResult) {
            console.log(`Cached PDF deleted for letter ${existingLetterId}`);
          } else {
            console.warn(`Failed to delete cached PDF for letter ${existingLetterId}`);
          }
        }
      }

      return NextResponse.json({ 
        data: {
          structuredData: editedStructuredData,
          design: designHtml,
          templateId,
          letterId: existingLetterId,
        }
       });
    }

    // Create database record with 'processing' status in letters table
    const { data: insertData, error: insertError } = await supabase
      .from('letters')
      .insert({
        user_id: userId,
        template_id: templateId,
        status: 'processing',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return NextResponse.json({ error: 'Failed to create letter generation record' }, { status: 500 });
    }

    const letterId = insertData.id as string;

    // Prepare Edge Function request
    const edgeFunctionRequest: LetterEdgeFunctionRequest = {
      letterId,
      resumeInput,
      jobInput,
      templateId
    };

    // Invoke the Supabase Edge Function asynchronously (fire and forget)
    callGenerateLetterEdgeFunction(edgeFunctionRequest).catch(error => {
      console.error('Letter Edge Function call failed:', error);
      captureApiError('generate-application-letter', error, {
        context: 'edge-function-call',
        userId,
        letterId
      });
    });

    // Track usage for initiation
    if (userId) {
      trackApiUsage(
        'letter-generation-initiated',
        'success',
        Date.now() - startTime,
        {
          has_job_description: !!jobDescription,
          has_job_image: !!jobImage,
          template_id: templateId,
          letterId
        },
        userId
      );
    }

    // Return immediately with the letter ID
    return NextResponse.json({ id: letterId });
  } catch (error) {
    console.error('Letter generation request error:', error);

    captureApiError('generate-application-letter', error, {
      context: 'letter-generation-request',
      userId
    });
    
    return NextResponse.json({
      error: 'Failed to process letter generation request'
    }, { status: 500 });
  }
}

// Export the handler with error reporting wrapper
export const POST = createApiHandler('generate-application-letter', handleRequest);
