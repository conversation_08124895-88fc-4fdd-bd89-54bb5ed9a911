import { NextRequest, NextResponse } from "next/server";
import { createClient } from "@/lib/supabase-server";
import { createApi<PERSON>andler } from "@/utils/apiErrorHandler";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import { StructuredResumeData } from "@/types/resume-structured";
import { convertToTemplateData, validateTemplateData } from "@/types/template-data";

/**
 * Delete PDF from Supabase storage
 */
async function deletePdfFromStorage(supabase: any, resumeId: string): Promise<boolean> {
  try {
    const filePath = `${resumeId}.pdf`;
    const { error } = await supabase.storage
      .from('generated-resumes')
      .remove([filePath]);

    if (error) {
      console.error('Error deleting PDF from storage:', error);
      return false;
    }

    return true;
  } catch (error) {
    console.error('Error deleting PDF from storage:', error);
    return false;
  }
}

async function handleGet(_req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const { id } = await params;
  const { data, error } = await supabase.from("resumes").select("*").eq("id", id).single();
  if (error) {
    return NextResponse.json({ error: error.message }, { status: 404 });
  }
  return NextResponse.json(data);
}

async function handlePut(req: NextRequest, { params }: { params: { id: string } }) {
  const supabase = await createClient();
  const body = (await req.json()) as {
    structured_data: StructuredResumeData;
    template_id?: string;
  };

  const { id } = await params;

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  // Validate required data
  if (!body.structured_data) {
    return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
  }

  if (!body.template_id) {
    return NextResponse.json({ error: "template_id is required" }, { status: 400 });
  }

  try {
    // Generate HTML from structured data using template engine
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    // Check if structured data has required fields for HTML generation
    let html: string | null = null;
    try {
      // Convert to template data and validate required fields
      const templateData = convertToTemplateData(body.structured_data);
      const validation = validateTemplateData(templateData);

      if (validation.isValid) {
        // Only generate HTML if required fields are present
        html = fillResumeTemplate(template, body.structured_data);
      }
      // If validation fails, we'll create the record without HTML
      // HTML will be generated later when user fills in required fields
    } catch (error) {
      console.log("Skipping HTML generation for empty manual resume:", error);
      // Continue without HTML - this is expected for empty manual resumes
    }

    // Update the resume with structured data, HTML, and template ID
    const { error } = await supabase
      .from("resumes")
      .update({
        structured_data: body.structured_data,
        html: html,
        template_id: body.template_id,
        updated_at: new Date().toISOString()
      })
      .eq("id", id)
      .eq("user_id", user.id); // Ensure user can only update their own resumes

    if (error) {
      return NextResponse.json({ error: error.message }, { status: 500 });
    }

    // Delete cached PDF from storage since the resume content has been updated
    const deletionResult = await deletePdfFromStorage(supabase, id);
    if (deletionResult) {
      console.log(`Cached PDF deleted for resume ${id}`);
    } else {
      console.warn(`Failed to delete cached PDF for resume ${id}`);
    }

    return NextResponse.json({
      success: true,
      message: "Resume updated successfully",
      html_generated: !!html,
      template_id: body.template_id,
      template_name: template.name
    });
  } catch (error) {
    console.error('Error updating resume:', error);
    return NextResponse.json({ error: "Failed to update resume" }, { status: 500 });
  }
}

export const GET = createApiHandler("resume-get", handleGet);
export const PUT = createApiHandler("resume-update", handlePut);
