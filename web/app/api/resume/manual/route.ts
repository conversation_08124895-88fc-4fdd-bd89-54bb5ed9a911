export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";
import { StructuredResumeData } from "@/types/resume-structured";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import { convertToTemplateData, validateTemplateData } from "@/types/template-data";

async function handlePostRequest(req: NextRequest) {
  const supabase = await createClient();
  
  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = (await req.json()) as {
      structured_data: StructuredResumeData;
      template_id: string;
    };

    // Validate required data
    if (!body.structured_data) {
      return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
    }

    if (!body.template_id) {
      return NextResponse.json({ error: "template_id is required" }, { status: 400 });
    }

    // Validate template exists
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    // Check if structured data has required fields for HTML generation
    let html: string | null = null;
    try {
      // Convert to template data and validate required fields
      const templateData = convertToTemplateData(body.structured_data);
      const validation = validateTemplateData(templateData);

      if (validation.isValid) {
        // Only generate HTML if required fields are present
        html = fillResumeTemplate(template, body.structured_data);
      }
      // If validation fails, we'll create the record without HTML
      // HTML will be generated later when user fills in required fields
    } catch (error) {
      console.log("Skipping HTML generation for empty manual resume:", error);
      // Continue without HTML - this is expected for empty manual resumes
    }

    // Create database record with 'done' status for manual resumes
    const { data: insertData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: user.id,
        structured_data: body.structured_data,
        html: html, // Will be null if required fields are missing
        template_id: body.template_id,
        status: 'done', // Manual resumes start as 'done' since they don't need AI processing
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return NextResponse.json({ error: 'Failed to create manual resume record' }, { status: 500 });
    }

    const id = insertData.id as string;

    return NextResponse.json({
      id,
      success: true,
      message: "Manual resume created successfully",
      template_id: body.template_id,
      template_name: template.name,
      html_generated: !!html
    });
  } catch (error) {
    console.error('Manual resume creation error:', error);
    return NextResponse.json({
      error: 'Failed to create manual resume'
    }, { status: 500 });
  }
}

export const POST = createApiHandler(
  "resume-manual-create",
  handlePostRequest
);
