export const dynamic = "force-dynamic";

import { NextRequest, NextResponse } from "next/server";
import { createApiHandler } from "@/utils/apiErrorHandler";
import { createClient } from "@/lib/supabase-server";
import { StructuredResumeData } from "@/types/resume-structured";
import { getResumeTemplateById } from "@/utils/resume-templates/resumeTemplates";
import { fillResumeTemplate } from "@/utils/template-engine";
import { convertToTemplateData, validateTemplateData } from "@/types/template-data";

async function handlePostRequest(req: NextRequest) {
  const supabase = await createClient();

  // Get current user
  const { data: { user }, error: authError } = await supabase.auth.getUser();
  if (authError || !user) {
    return NextResponse.json({ error: "Unauthorized" }, { status: 401 });
  }

  try {
    const body = (await req.json()) as {
      structured_data: StructuredResumeData;
      template_id: string;
    };

    // Validate required data
    if (!body.structured_data) {
      return NextResponse.json({ error: "structured_data is required" }, { status: 400 });
    }

    if (!body.template_id) {
      return NextResponse.json({ error: "template_id is required" }, { status: 400 });
    }

    // Validate template exists
    const template = getResumeTemplateById(body.template_id);
    if (!template) {
      return NextResponse.json({ error: "Invalid template_id provided" }, { status: 400 });
    }

    // Check if user already has a manual resume (status = 'done' with empty or minimal structured_data)
    // This prevents duplicate empty manual resumes
    const { data: existingResumes, error: queryError } = await supabase
      .from('resumes')
      .select('id, structured_data, created_at')
      .eq('user_id', user.id)
      .eq('status', 'done')
      .order('created_at', { ascending: false })
      .limit(5); // Check last 5 resumes to avoid performance issues

    if (queryError) {
      console.error('Error checking existing resumes:', queryError);
      // Continue with creation if query fails - better to have duplicate than block user
    } else if (existingResumes && existingResumes.length > 0) {
      // Check if any of the recent resumes are essentially empty manual resumes
      const isEmptyResume = (resumeData: any) => {
        if (!resumeData || !resumeData.structured_data) return true;

        const data = resumeData.structured_data;
        // Consider a resume empty if it has no meaningful content
        const hasPersonalInfo = data.personalInfo?.fullName?.trim() ||
                               data.personalInfo?.email?.trim() ||
                               data.personalInfo?.phone?.trim();
        const hasExperiences = data.experiences?.length > 0 &&
                              data.experiences.some((exp: any) => exp.jobTitle?.trim() || exp.company?.trim());
        const hasEducation = data.education?.length > 0 &&
                            data.education.some((edu: any) => edu.degree?.trim() || edu.institution?.trim());
        const hasSkills = data.skills?.categories?.length > 0 &&
                         data.skills.categories.some((cat: any) => cat.skills?.length > 0 &&
                         cat.skills.some((skill: string) => skill?.trim()));

        return !hasPersonalInfo && !hasExperiences && !hasEducation && !hasSkills;
      };

      // Find the most recent empty manual resume
      const recentEmptyResume = existingResumes.find(resume => isEmptyResume(resume));

      if (recentEmptyResume) {
        // Return the existing empty resume instead of creating a new one
        console.log(`Returning existing empty manual resume ${recentEmptyResume.id} for user ${user.id}`);
        return NextResponse.json({
          id: recentEmptyResume.id,
          success: true,
          message: "Using existing manual resume",
          template_id: body.template_id,
          template_name: template.name,
          html_generated: false,
          is_existing: true
        });
      }
    }

    // Check if structured data has required fields for HTML generation
    let html: string | null = null;
    try {
      // Convert to template data and validate required fields
      const templateData = convertToTemplateData(body.structured_data);
      const validation = validateTemplateData(templateData);

      if (validation.isValid) {
        // Only generate HTML if required fields are present
        html = fillResumeTemplate(template, body.structured_data);
      }
      // If validation fails, we'll create the record without HTML
      // HTML will be generated later when user fills in required fields
    } catch (error) {
      console.log("Skipping HTML generation for empty manual resume:", error);
      // Continue without HTML - this is expected for empty manual resumes
    }

    // Create database record with 'done' status for manual resumes
    const { data: insertData, error: insertError } = await supabase
      .from('resumes')
      .insert({
        user_id: user.id,
        structured_data: body.structured_data,
        html: html, // Will be null if required fields are missing
        template_id: body.template_id,
        status: 'done', // Manual resumes start as 'done' since they don't need AI processing
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select('id')
      .single();

    if (insertError || !insertData) {
      console.error('Supabase insert error:', insertError);
      return NextResponse.json({ error: 'Failed to create manual resume record' }, { status: 500 });
    }

    const id = insertData.id as string;

    return NextResponse.json({
      id,
      success: true,
      message: "Manual resume created successfully",
      template_id: body.template_id,
      template_name: template.name,
      html_generated: !!html
    });
  } catch (error) {
    console.error('Manual resume creation error:', error);
    return NextResponse.json({
      error: 'Failed to create manual resume'
    }, { status: 500 });
  }
}

export const POST = createApiHandler(
  "resume-manual-create",
  handlePostRequest
);
